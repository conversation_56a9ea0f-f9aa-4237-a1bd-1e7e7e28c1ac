<?php

use models\standard\Dates;
use models\standard\Strings;
global $myFileInfo;

$drawRequestHistory = $drawRequestManager->getDrawRequestHistory();

// Format data with fallback values
$address = Strings::showField('propertyAddress', 'LMRInfo');
$city = Strings::showField('propertyCity', 'LMRInfo');
$state = Strings::showField('propertyState', 'LMRInfo');
$zip = Strings::showField('propertyZip', 'LMRInfo');

$initialLoan = Strings::showField('InitialLoanAmount', 'fileCalculatedValues');
$rehabCostFinanced = Strings::showField('rehabCostFinanced', 'fileHMLONewLoanInfo');
$totalLoanAmount = Strings::showField('totalLoanAmount', 'fileHMLONewLoanInfo');
$currentLoanBalance = Strings::showField('CurrentLoanBalance', 'fileCalculatedValues');
$rehabCost = Strings::showField('rehabCost', 'fileHMLOInfo');
$arv = Strings::showField('FullARV', 'fileCalculatedValues') . '%';

$closingDate = Dates::formatDateWithRE(Strings::showField('closingDate', 'QAInfo'), 'YMD', 'm/d/Y');
$maturityDate = Dates::formatDateWithRE(Strings::showField('maturityDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y');

$totalApproved = array_column($drawRequestHistory, 'approvedAmount');
$totalDrawsFunded = array_sum($totalApproved);

$holdbackRemaining = $rehabCostFinanced > 0 ? $rehabCostFinanced - $totalDrawsFunded : 0;

// Format the numbers for display
$totalDrawsFunded = number_format($totalDrawsFunded);
$holdbackRemaining = number_format($holdbackRemaining);

$lastDrawRequest = end($drawRequestHistory);
$lastApprovedDrawRequest = null;
// Find the latest approved draw request
foreach (array_reverse($drawRequestHistory) as $history) {
    if ($history['status'] === 'approved') {
        $lastApprovedDrawRequest = $history;
        break;
    }
}
$dateOfCurrentDraw = '-';
$dateOfLastDraw = '-';
if($lastDrawRequest['status'] !== 'approved') $dateOfCurrentDraw = date('m/d/Y', strtotime($lastDrawRequest['submittedAt']));
if($lastApprovedDrawRequest && $requestData['isDrawRequest']) $dateOfLastDraw = date('m/d/Y', strtotime($lastApprovedDrawRequest['approvedAt']));

?>

<style>
.summary-section {
    background: #fff;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-section h4 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.summary-grid {
    margin-bottom: 15px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

.summary-label {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    font-size: 1rem;
    font-weight: 500;
}


</style>

<div class="summary-section">
    <h4>Summary</h4>

    <div class="summary-grid">
        <!-- Row 1: Address Information -->
        <div class="row">
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Address</div>
                    <div class="summary-value text-muted"><?= htmlspecialchars($address); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">City</div>
                    <div class="summary-value text-muted"><?= htmlspecialchars($city); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">State</div>
                    <div class="summary-value text-muted"><?= htmlspecialchars($state); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Zip</div>
                    <div class="summary-value text-muted"><?= htmlspecialchars($zip); ?></div>
                </div>
            </div>
        </div>

        <!-- Row 2: Loan Information -->
        <div class="row">
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Initial Loan</div>
                    <div class="summary-value text-success">$<?= number_format($initialLoan); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Rehab Cost Financed</div>
                    <div class="summary-value text-success">$<?= number_format($rehabCostFinanced); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Total Loan Amount</div>
                    <div class="summary-value text-success">$<?= number_format($totalLoanAmount); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Current Loan Balance</div>
                    <div class="summary-value text-success">$<?= number_format($currentLoanBalance); ?></div>
                </div>
            </div>
        </div>

        <!-- Row 3: Additional Loan Information -->
        <div class="row">
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Rehab Cost</div>
                    <div class="summary-value text-success">$<?= number_format($rehabCost); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">ARV</div>
                    <div class="summary-value text-success"><?= $arv; ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Total Draws Funded</div>
                    <div class="summary-value text-success">$<?= htmlspecialchars($totalDrawsFunded); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Holdback Remaining</div>
                    <div class="summary-value text-success">$<?= htmlspecialchars($holdbackRemaining); ?></div>
                </div>
            </div>
        </div>

        <!-- Row 4: Date Information -->
        <div class="row">
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Closing Date</div>
                    <div class="summary-value text-primary"><?= htmlspecialchars($closingDate); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Maturity Date</div>
                    <div class="summary-value text-primary"><?= htmlspecialchars($maturityDate); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Date of Last Draw</div>
                    <div class="summary-value text-primary"><?= htmlspecialchars($dateOfLastDraw); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Date of Current Draw</div>
                    <div class="summary-value text-primary"><?= htmlspecialchars($dateOfCurrentDraw); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>
