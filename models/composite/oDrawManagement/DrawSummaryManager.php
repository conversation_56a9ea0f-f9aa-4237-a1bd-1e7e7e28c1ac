<?php

namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\standard\Strings;
use modals\standard\Dates;

class DrawSummaryManager extends strongType
{

    private ?int $LMRId = null;

    public function __construct(int $LMRId, ?DrawRequestManager $drawRequestManager = null)
    {
        $this->LMRId = $LMRId;
        if ($drawRequestManager == null) {
            $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
        }

        $drawRequestHistory = $drawRequestManager->getDrawRequestHistory();

    }
}
